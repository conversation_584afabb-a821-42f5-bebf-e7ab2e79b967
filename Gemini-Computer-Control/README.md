# Gemini Computer Control

An AI-powered computer automation system that uses **Google's Gemini 2.0 Flash Live 001** model with **live video streaming** to provide real-time computer control through natural language instructions.

## ⚠️ Important Safety Notice

This application gives AI control over your computer's mouse and keyboard. Please read the security considerations carefully before use.

## Features

### 🚀 **NEW: Live Video Streaming with Gemini 2.0 Flash Live 001**
- **Real-time Video Feed**: Streams live video of your screen to Gemini Live API
- **Ultra-low Latency**: Immediate response to screen changes and user actions
- **Advanced AI Model**: Uses the latest Gemini 2.0 Flash Live 001 for superior understanding
- **Bidirectional Streaming**: WebSocket-based communication for real-time interaction

### Core Features
- **Live Screen Analysis**: Continuously streams and analyzes your screen using Gemini Live API
- **Natural Language Control**: Give instructions in plain English with real-time feedback
- **Enhanced Context System**: AI learns from previous commands, failures, and user interactions
- **Interactive Commands**: User input requests, task completion tracking, and hotkey execution
- **Safety Features**: Built-in safeguards, confirmations, and emergency stops
- **Autonomous Mode**: Continuous monitoring and action execution with live video feed
- **Single Command Mode**: Execute one-off instructions with immediate video analysis
- **Session Management**: Automatic reconnection and session health monitoring
- **Comprehensive Logging**: Detailed logs of all actions and decisions

## Installation

### Prerequisites

- Python 3.9 or higher
- PDM (Python Dependency Manager)
- Google Gemini API key

### Setup

1. **Clone and navigate to the project:**
   ```bash
   cd Gemini-Computer-Control
   ```

2. **Install PDM** (if not already installed):
   ```bash
   pip install pdm
   ```

3. **Install dependencies:**
   ```bash
   pdm install
   ```

4. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your Gemini API key and preferences
   ```

5. **Get a Gemini API key:**
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add it to your `.env` file

## Configuration

Edit the `.env` file to configure the application:

```env
# Required
GEMINI_API_KEY=your_api_key_here

# Live API Video Streaming (NEW)
VIDEO_FRAME_RATE=1.0                    # FPS for live video feed
VIDEO_MAX_DIMENSION=1280                # Max width/height for video frames
VIDEO_QUALITY=70                        # JPEG quality for video frames

# Live API Session Management (NEW)
LIVE_API_RESPONSE_MODALITY=TEXT         # TEXT or AUDIO
LIVE_API_SESSION_TIMEOUT=300            # Session timeout in seconds
LIVE_API_RECONNECT_ATTEMPTS=3           # Number of reconnection attempts
LIVE_API_RECONNECT_DELAY=5.0            # Delay between reconnection attempts

# Legacy Settings (still supported)
SCREEN_CAPTURE_INTERVAL=2.0             # For single-shot captures
SCREEN_CAPTURE_QUALITY=80               # For single-shot captures
MAX_REQUESTS_PER_MINUTE=30              # Rate limiting (less relevant for Live API)

# Safety Settings
CONFIRMATION_REQUIRED=true
SAFE_MODE=true
```

## Usage

### Check Status
```bash
pdm run gemini-control status
```

### Test Screen Capture
```bash
pdm run gemini-control test-capture
```

### Test New Command Types
```bash
pdm run gemini-control test-commands
```

### Test Enhanced Context System
```bash
pdm run gemini-control test-context
```

### Single Command Mode
Execute a single instruction:
```bash
pdm run gemini-control single "Click on the calculator app and add 2 + 3"
```

### Autonomous Mode
Start continuous monitoring (use with caution):
```bash
pdm run gemini-control autonomous --instruction "Help me organize my desktop"
```

## Safety Features

### Built-in Protections
- **Safe Mode**: Validates all commands before execution
- **Confirmation Prompts**: Requires approval for potentially dangerous actions
- **Emergency Stop**: Press `Ctrl+Shift+Q` to immediately stop
- **Rate Limiting**: Prevents API abuse
- **Command Validation**: Filters out malicious or invalid commands
- **Application Whitelist**: Restricts actions to approved applications

### Security Considerations

⚠️ **IMPORTANT**: This application can control your computer. Consider these risks:

1. **Data Privacy**: Screen captures are sent to Google's Gemini API
2. **Unintended Actions**: AI might misinterpret instructions
3. **API Costs**: Continuous use may incur significant API charges
4. **System Access**: The application can interact with any visible interface

### Recommended Safety Practices

1. **Start with Safe Mode enabled** (default)
2. **Use confirmation prompts** for dangerous actions (default)
3. **Test with single commands** before using autonomous mode
4. **Monitor the application** when running autonomously
5. **Use application whitelists** to limit scope
6. **Keep emergency stop key** easily accessible
7. **Review logs regularly** to understand AI decisions

## Architecture

### Core Components

1. **Screen Recorder** (`screen_recorder.py`)
   - Captures screenshots using `mss`
   - Optimizes image size and quality
   - Handles multiple monitors

2. **Gemini Client** (`gemini_client.py`)
   - Interfaces with Google's Gemini API
   - Processes images and returns structured commands
   - Implements rate limiting and error handling

3. **Computer Controller** (`computer_controller.py`)
   - Executes commands using `pyautogui`
   - Validates coordinates and inputs
   - Implements safety checks

4. **Command Interpreter** (`command_interpreter.py`)
   - Orchestrates the entire workflow
   - Manages autonomous and single-command modes
   - Maintains command history

### Command Format

The AI returns commands in JSON format:

```json
{
  "commands": [
    {"action": "click", "x": 100, "y": 200},
    {"action": "type", "text": "Hello World"},
    {"action": "key_press", "key": "enter"}
  ],
  "message": "Clicked on text field and typed 'Hello World'"
}
```

### Supported Actions

#### Basic Actions
- `click`: Click at coordinates
- `type`: Type text
- `key_press`: Press a key
- `scroll`: Scroll up/down
- `move_mouse`: Move mouse cursor
- `wait`: Pause execution

#### Interactive Actions
- `request_input`: Request user input with prompts and choices
- `task_complete`: Signal task completion with status and results
- `hotkey`: Execute keyboard shortcuts and key combinations

### Command Examples

#### User Input Commands
```json
{
  "action": "request_input",
  "prompt": "What is your name?",
  "input_type": "text"
}

{
  "action": "request_input",
  "prompt": "Choose an option:",
  "input_type": "choice",
  "choices": ["Option 1", "Option 2", "Option 3"]
}
```

#### Task Completion Commands
```json
{
  "action": "task_complete",
  "status": "success",
  "message": "Successfully organized 25 files",
  "results": {"files_processed": 25, "time_taken": "2.5s"}
}
```

#### Hotkey Commands
```json
{
  "action": "hotkey",
  "keys": "ctrl+c"
}

{
  "action": "hotkey",
  "keys": "cmd+shift+n"
}
```

## Development

### Running Tests
```bash
pdm run pytest
```

### Code Formatting
```bash
pdm run black src/
pdm run isort src/
```

### Type Checking
```bash
pdm run mypy src/
```

## Ethical Considerations

This technology raises important ethical questions:

1. **Consent**: Ensure all users of shared computers consent to AI control
2. **Privacy**: Be aware that screen content is sent to external APIs
3. **Responsibility**: Users are responsible for AI actions on their systems
4. **Transparency**: Log and review all AI decisions
5. **Limitations**: Understand the AI's capabilities and limitations

## Troubleshooting

### Common Issues

1. **API Key Error**: Ensure your Gemini API key is valid and has sufficient quota
2. **Screen Capture Fails**: Check permissions for screen recording on macOS
3. **Commands Not Executing**: Verify pyautogui permissions and safe mode settings
4. **Rate Limiting**: Reduce capture frequency or increase rate limits

### Logs

Check the log file (`gemini_control.log`) for detailed information about:
- API requests and responses
- Command execution details
- Error messages and stack traces

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## Disclaimer

This software is provided "as is" without warranty. Users assume all risks associated with AI-controlled computer automation. The authors are not responsible for any damage or unintended consequences resulting from the use of this software.
