"""Screen recording functionality using mss."""

import asyncio
import base64
import io
import time
from typing import Op<PERSON>, <PERSON><PERSON>, Callable, AsyncGenerator

import mss
from loguru import logger
from PIL import Image

from .config import settings


class ScreenRecorder:
    """Handles screen capture and image processing."""

    def __init__(self):
        self.sct = mss.mss()
        self.monitor = self.sct.monitors[settings.screen_monitor + 1]  # +1 because index 0 is all monitors
        self.last_capture_time = 0
        self.is_recording = False

    def capture_screen(self) -> Optional[bytes]:
        """
        Capture the current screen and return as JPEG bytes.

        Returns:
            JPEG image data as bytes, or None if capture fails
        """
        try:
            # Capture screenshot
            screenshot = self.sct.grab(self.monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

            # Resize if too large (to reduce API payload size)
            max_dimension = 1920
            if max(img.size) > max_dimension:
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            # Convert to JPEG bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format="JPEG", quality=settings.screen_capture_quality)
            img_buffer.seek(0)

            self.last_capture_time = time.time()
            logger.debug(f"Screen captured: {img.size}, {len(img_buffer.getvalue())} bytes")

            return img_buffer.getvalue()

        except Exception as e:
            logger.error(f"Failed to capture screen: {e}")
            return None

    def capture_video_frame(self) -> Optional[str]:
        """
        Capture a video frame optimized for Live API streaming.

        Returns:
            Base64 encoded JPEG image data, or None if capture fails
        """
        try:
            # Capture screenshot
            screenshot = self.sct.grab(self.monitor)

            # Convert to PIL Image
            img = Image.frombytes("RGB", screenshot.size, screenshot.bgra, "raw", "BGRX")

            # Resize for video streaming (smaller for better performance)
            max_dimension = settings.video_max_dimension
            if max(img.size) > max_dimension:
                ratio = max_dimension / max(img.size)
                new_size = tuple(int(dim * ratio) for dim in img.size)
                img = img.resize(new_size, Image.Resampling.LANCZOS)

            # Convert to JPEG bytes with video quality
            img_buffer = io.BytesIO()
            img.save(img_buffer, format="JPEG", quality=settings.video_quality)
            img_buffer.seek(0)

            # Encode to base64 for Live API
            image_data = img_buffer.getvalue()
            base64_data = base64.b64encode(image_data).decode('utf-8')

            self.last_capture_time = time.time()
            logger.debug(f"Video frame captured: {img.size}, {len(base64_data)} chars")

            return base64_data

        except Exception as e:
            logger.error(f"Failed to capture video frame: {e}")
            return None

    async def start_continuous_capture(self, callback) -> None:
        """
        Start continuous screen capture with callback.

        Args:
            callback: Async function to call with each captured image
        """
        self.is_recording = True
        logger.info("Starting continuous screen capture")

        try:
            while self.is_recording:
                # Capture screen
                image_data = self.capture_screen()

                if image_data:
                    # Call the callback with the image data
                    await callback(image_data)

                # Wait for the specified interval
                await asyncio.sleep(settings.screen_capture_interval)

        except asyncio.CancelledError:
            logger.info("Screen capture cancelled")
        except Exception as e:
            logger.error(f"Error in continuous capture: {e}")
        finally:
            self.is_recording = False

    def stop_capture(self) -> None:
        """Stop continuous screen capture."""
        self.is_recording = False
        logger.info("Stopping screen capture")

    async def start_video_stream(self) -> AsyncGenerator[str, None]:
        """
        Start a video stream generator for Live API.

        Yields:
            Base64 encoded video frames at the configured frame rate
        """
        self.is_recording = True
        logger.info(f"Starting video stream at {settings.video_frame_rate} FPS")

        frame_interval = 1.0 / settings.video_frame_rate

        try:
            while self.is_recording:
                frame_start_time = time.time()

                # Capture video frame
                frame_data = self.capture_video_frame()

                if frame_data:
                    yield frame_data

                # Calculate sleep time to maintain frame rate
                frame_duration = time.time() - frame_start_time
                sleep_time = max(0, frame_interval - frame_duration)

                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)

        except asyncio.CancelledError:
            logger.info("Video stream cancelled")
        except Exception as e:
            logger.error(f"Error in video stream: {e}")
        finally:
            self.is_recording = False

    def get_screen_info(self) -> dict:
        """Get information about the current screen setup."""
        return {
            "monitor_count": len(self.sct.monitors) - 1,  # -1 because index 0 is all monitors
            "current_monitor": settings.screen_monitor,
            "monitor_size": (self.monitor["width"], self.monitor["height"]),
            "capture_interval": settings.screen_capture_interval,
            "quality": settings.screen_capture_quality
        }

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop_capture()
        if hasattr(self.sct, 'close'):
            self.sct.close()
