"""Command interpretation and orchestration."""

import asyncio
from typing import Dict, List, Optional

from loguru import logger

from .computer_controller import ComputerController
from .gemini_client import GeminiClient
from .screen_recorder import ScreenRecorder


class CommandInterpreter:
    """Orchestrates the interaction between screen recording, AI analysis, and computer control."""

    def __init__(self):
        self.screen_recorder = ScreenRecorder()
        self.gemini_client = GeminiClient()
        self.computer_controller = ComputerController()

        self.is_running = False
        self.current_instruction = ""
        self.command_history = []

        # Track user interactions and task completions
        self.user_interactions = []
        self.completed_tasks = []

        # Enhanced context tracking
        self.execution_failures = []
        self.retry_attempts = {}
        self.context_window_size = 5  # Number of recent commands to include in context

    async def start_autonomous_mode(self, initial_instruction: str = "") -> None:
        """
        Start autonomous mode with live video streaming to Gemini Live API.

        Args:
            initial_instruction: Initial instruction for what the AI should accomplish
        """
        self.current_instruction = initial_instruction
        self.is_running = True

        logger.info("Starting autonomous mode with Live API")
        if initial_instruction:
            logger.info(f"Initial instruction: {initial_instruction}")

        try:
            # Generate initial context
            context = self._generate_ai_context(initial_instruction)

            # Create Live API session context manager
            session_cm = await self.gemini_client.create_live_session()

            # Use proper async with context manager
            async with session_cm as session:
                # Setup the session
                await self.gemini_client.setup_session(session, initial_instruction, context)

                # Start video streaming and response processing
                await asyncio.gather(
                    self._stream_video_to_live_api(),
                    self._process_live_responses()
                )

        except KeyboardInterrupt:
            logger.info("Autonomous mode interrupted by user")
        except Exception as e:
            logger.error(f"Error in autonomous mode: {e}")
        finally:
            await self.stop()

    async def execute_single_instruction(self, instruction: str) -> Dict:
        """
        Execute a single instruction using Live API with a single video frame.

        Args:
            instruction: What the AI should try to accomplish

        Returns:
            Dictionary with execution results
        """
        logger.info(f"Executing single instruction with Live API: {instruction}")

        try:
            # Generate context for the AI
            context = self._generate_ai_context(instruction)

            # Create Live API session context manager
            session_cm = await self.gemini_client.create_live_session()

            # Use proper async with context manager
            async with session_cm as session:
                # Setup the session
                await self.gemini_client.setup_session(session, instruction, context)

                # Capture and send a single video frame
                frame_data = self.screen_recorder.capture_video_frame()
                if not frame_data:
                    return {"success": False, "error": "Failed to capture video frame"}

                # Send frame to Live API
                await self.gemini_client.send_video_frame(frame_data)

                # Wait for response
                analysis_result = await self.gemini_client.get_next_response(timeout=30.0)
                if not analysis_result:
                    return {"success": False, "error": "No response from Live API"}

                # Validate commands
                commands = await self.gemini_client.validate_commands(
                    analysis_result.get("commands", [])
                )

                if not commands:
                    return {
                        "success": True,
                        "message": analysis_result.get("message", "No commands to execute"),
                        "commands_executed": 0
                    }

                # Execute commands
                executed_count = await self.computer_controller.execute_commands(commands)

                # Track execution results
                self._track_execution_results(instruction, commands, executed_count)

                # Check for user interactions and task completions
                await self._process_special_commands(commands)

                # Record in history
                history_entry = {
                    "instruction": instruction,
                    "analysis": analysis_result,
                    "commands": commands,
                    "executed_count": executed_count,
                    "total_commands": len(commands),
                    "success_rate": executed_count / len(commands) if commands else 1.0,
                    "timestamp": asyncio.get_event_loop().time()
                }
                self.command_history.append(history_entry)

                return {
                    "success": True,
                    "message": analysis_result.get("message", "Commands executed"),
                    "commands_executed": executed_count,
                    "total_commands": len(commands)
                }

        except Exception as e:
            logger.error(f"Error in single instruction execution: {e}")
            return {"success": False, "error": str(e)}

    async def _process_screen_capture(self, image_data: bytes) -> None:
        """
        Process a screen capture by analyzing it with AI and executing commands.

        Args:
            image_data: JPEG image data from screen capture
        """
        if not self.is_running:
            return

        try:
            # Generate context for the AI
            context = self._generate_ai_context(self.current_instruction)

            # Analyze the screen with current instruction and context
            analysis_result = await self.gemini_client.analyze_screen(
                image_data,
                self.current_instruction,
                context
            )

            if not analysis_result:
                logger.warning("Failed to analyze screen capture")
                return

            # Get commands from analysis
            commands = analysis_result.get("commands", [])
            message = analysis_result.get("message", "")

            if message:
                logger.info(f"AI message: {message}")

            if not commands:
                logger.debug("No commands to execute")
                return

            # Validate commands
            safe_commands = await self.gemini_client.validate_commands(commands)

            if not safe_commands:
                logger.warning("No safe commands to execute")
                return

            # Execute commands
            logger.info(f"Executing {len(safe_commands)} commands")
            executed_count = await self.computer_controller.execute_commands(safe_commands)

            # Track execution results
            self._track_execution_results(self.current_instruction, safe_commands, executed_count)

            # Check for user interactions and task completions
            await self._process_special_commands(safe_commands)

            # Record in history
            history_entry = {
                "instruction": self.current_instruction,
                "analysis": analysis_result,
                "commands": safe_commands,
                "executed_count": executed_count,
                "total_commands": len(safe_commands),
                "success_rate": executed_count / len(safe_commands) if safe_commands else 1.0,
                "timestamp": asyncio.get_event_loop().time()
            }
            self.command_history.append(history_entry)

            # Limit history size
            if len(self.command_history) > 100:
                self.command_history = self.command_history[-50:]

        except Exception as e:
            logger.error(f"Error processing screen capture: {e}")

    async def stop(self) -> None:
        """Stop the command interpreter."""
        self.is_running = False
        self.screen_recorder.stop_capture()

        # Clean up Live API session
        if hasattr(self.gemini_client, 'cleanup_session'):
            await self.gemini_client.cleanup_session()

        logger.info("Command interpreter stopped")

    async def _stream_video_to_live_api(self) -> None:
        """Stream video frames to the Live API."""
        logger.info("Starting video stream to Live API")

        try:
            async for frame_data in self.screen_recorder.start_video_stream():
                if not self.is_running:
                    break

                # Send frame to Live API
                await self.gemini_client.send_video_frame(frame_data)

                # Check session health
                if not self.gemini_client.is_session_healthy():
                    logger.warning("Live API session unhealthy, attempting reconnection")
                    await self._reconnect_live_api()

        except Exception as e:
            logger.error(f"Error in video streaming: {e}")

    async def _process_live_responses(self) -> None:
        """Process responses from the Live API."""
        logger.info("Starting Live API response processing")

        while self.is_running:
            try:
                # Get next response with timeout
                response = await self.gemini_client.get_next_response(timeout=5.0)

                if response is None:
                    continue

                # Process the response
                await self._handle_live_response(response)

            except Exception as e:
                logger.error(f"Error processing live response: {e}")
                await asyncio.sleep(1)  # Brief pause before retrying

    async def _handle_live_response(self, response: Dict) -> None:
        """Handle a response from the Live API."""
        try:
            commands = response.get("commands", [])
            message = response.get("message", "")

            if message:
                logger.info(f"AI message: {message}")

            if not commands:
                logger.debug("No commands in response")
                return

            # Validate commands
            safe_commands = await self.gemini_client.validate_commands(commands)

            if not safe_commands:
                logger.warning("No safe commands to execute")
                return

            # Execute commands
            logger.info(f"Executing {len(safe_commands)} commands from Live API")
            executed_count = await self.computer_controller.execute_commands(safe_commands)

            # Track execution results
            self._track_execution_results(self.current_instruction, safe_commands, executed_count)

            # Check for user interactions and task completions
            await self._process_special_commands(safe_commands)

            # Record in history
            history_entry = {
                "instruction": self.current_instruction,
                "analysis": response,
                "commands": safe_commands,
                "executed_count": executed_count,
                "total_commands": len(safe_commands),
                "success_rate": executed_count / len(safe_commands) if safe_commands else 1.0,
                "timestamp": asyncio.get_event_loop().time()
            }
            self.command_history.append(history_entry)

            # Limit history size
            if len(self.command_history) > 100:
                self.command_history = self.command_history[-50:]

        except Exception as e:
            logger.error(f"Error handling live response: {e}")

    async def _reconnect_live_api(self) -> None:
        """Reconnect to the Live API."""
        try:
            logger.info("Reconnecting to Live API")

            # Clean up current session
            await self.gemini_client.cleanup_session()

            # Note: Reconnection would need to be handled at the autonomous mode level
            # since we need to re-establish the async with context manager
            logger.warning("Live API reconnection requires restarting autonomous mode")

        except Exception as e:
            logger.error(f"Error during Live API reconnection: {e}")

    def update_instruction(self, new_instruction: str) -> None:
        """Update the current instruction for autonomous mode."""
        self.current_instruction = new_instruction
        logger.info(f"Updated instruction: {new_instruction}")

    def get_status(self) -> Dict:
        """Get current status of the command interpreter."""
        return {
            "is_running": self.is_running,
            "current_instruction": self.current_instruction,
            "commands_in_history": len(self.command_history),
            "screen_info": self.screen_recorder.get_screen_info()
        }

    def get_command_history(self, limit: int = 10) -> List[Dict]:
        """Get recent command history."""
        return self.command_history[-limit:] if self.command_history else []

    def clear_history(self) -> None:
        """Clear command history."""
        self.command_history.clear()
        logger.info("Command history cleared")

    async def _process_special_commands(self, commands: List[Dict]) -> None:
        """Process special commands like user input and task completion."""
        for command in commands:
            action = command.get("action")

            if action == "request_input":
                # Record the user interaction
                user_input = self.computer_controller.get_user_input_response()
                if user_input is not None:
                    interaction = {
                        "prompt": command.get("prompt"),
                        "input_type": command.get("input_type", "text"),
                        "response": user_input,
                        "timestamp": asyncio.get_event_loop().time()
                    }
                    self.user_interactions.append(interaction)
                    logger.info(f"Recorded user interaction: {interaction}")

                    # Clear the response for next time
                    self.computer_controller.clear_user_input_response()

            elif action == "task_complete":
                # Record the task completion
                task_result = self.computer_controller.get_latest_task_result()
                if task_result:
                    self.completed_tasks.append(task_result)
                    logger.info(f"Recorded task completion: {task_result}")

    def get_user_interactions(self) -> List[Dict]:
        """Get all recorded user interactions."""
        return self.user_interactions.copy()

    def get_completed_tasks(self) -> List[Dict]:
        """Get all completed tasks."""
        return self.completed_tasks.copy()

    def get_latest_user_interaction(self) -> Optional[Dict]:
        """Get the most recent user interaction."""
        return self.user_interactions[-1] if self.user_interactions else None

    def get_latest_completed_task(self) -> Optional[Dict]:
        """Get the most recent completed task."""
        return self.completed_tasks[-1] if self.completed_tasks else None

    def clear_user_interactions(self) -> None:
        """Clear all recorded user interactions."""
        self.user_interactions.clear()
        logger.info("User interactions cleared")

    def clear_completed_tasks(self) -> None:
        """Clear all completed tasks."""
        self.completed_tasks.clear()
        logger.info("Completed tasks cleared")

    def get_enhanced_status(self) -> Dict:
        """Get enhanced status including new command capabilities."""
        base_status = self.get_status()

        # Add information about new capabilities
        base_status.update({
            "user_interactions_count": len(self.user_interactions),
            "completed_tasks_count": len(self.completed_tasks),
            "has_pending_user_input": self.computer_controller.has_pending_user_input(),
            "latest_task_status": (
                self.get_latest_completed_task()["status"]
                if self.get_latest_completed_task() else None
            ),
            "supported_commands": [
                "click", "type", "key_press", "scroll", "move_mouse", "wait",
                "request_input", "task_complete", "hotkey"
            ]
        })

        return base_status

    def _generate_ai_context(self, current_instruction: str) -> str:
        """
        Generate rich context information for the AI agent.

        Args:
            current_instruction: The current instruction being processed

        Returns:
            Formatted context string for the AI
        """
        context_parts = []

        # Current session info
        context_parts.append("=== CURRENT SESSION CONTEXT ===")
        context_parts.append(f"Current instruction: {current_instruction}")
        context_parts.append(f"Total commands executed in session: {len(self.command_history)}")

        # Recent command history
        recent_history = self.get_command_history(self.context_window_size)
        if recent_history:
            context_parts.append(f"\n=== RECENT COMMAND HISTORY (Last {len(recent_history)} commands) ===")
            for i, entry in enumerate(recent_history, 1):
                success_rate = entry.get("success_rate", 0)
                status = "✅" if success_rate == 1.0 else "⚠️" if success_rate > 0.5 else "❌"
                context_parts.append(
                    f"{i}. {status} Instruction: '{entry['instruction']}' "
                    f"({entry['executed_count']}/{entry.get('total_commands', 0)} commands succeeded)"
                )

                # Show failed commands if any
                if success_rate < 1.0:
                    failed_commands = self._get_failed_commands_from_entry(entry)
                    if failed_commands:
                        context_parts.append(f"   Failed commands: {failed_commands}")

        # Recent failures and patterns
        recent_failures = self._get_recent_failures()
        if recent_failures:
            context_parts.append(f"\n=== RECENT FAILURES (Last {len(recent_failures)}) ===")
            for i, failure in enumerate(recent_failures, 1):
                context_parts.append(f"{i}. {failure['error_type']}: {failure['command']} - {failure['reason']}")

        # Retry attempts
        if self.retry_attempts:
            context_parts.append(f"\n=== RETRY PATTERNS ===")
            for instruction, count in self.retry_attempts.items():
                if count > 1:
                    context_parts.append(f"- '{instruction}': {count} attempts")

        # User interactions context
        recent_interactions = self.user_interactions[-3:] if self.user_interactions else []
        if recent_interactions:
            context_parts.append(f"\n=== RECENT USER INTERACTIONS ===")
            for i, interaction in enumerate(recent_interactions, 1):
                context_parts.append(
                    f"{i}. Asked: '{interaction['prompt']}' -> User responded: '{interaction['response']}'"
                )

        # Task completion context
        recent_tasks = self.completed_tasks[-3:] if self.completed_tasks else []
        if recent_tasks:
            context_parts.append(f"\n=== RECENT TASK COMPLETIONS ===")
            for i, task in enumerate(recent_tasks, 1):
                status_emoji = {"success": "✅", "partial": "⚠️", "failed": "❌"}.get(task['status'], "ℹ️")
                context_parts.append(f"{i}. {status_emoji} {task['status'].upper()}: {task['message']}")

        # Success patterns and recommendations
        success_patterns = self._analyze_success_patterns()
        if success_patterns:
            context_parts.append(f"\n=== SUCCESS PATTERNS ===")
            context_parts.extend(success_patterns)

        # Current state indicators
        context_parts.append(f"\n=== CURRENT STATE ===")
        if self.computer_controller.has_pending_user_input():
            context_parts.append("⚠️ There is a pending user input request")

        latest_task = self.get_latest_completed_task()
        if latest_task:
            status_emoji = {"success": "✅", "partial": "⚠️", "failed": "❌"}.get(latest_task['status'], "ℹ️")
            context_parts.append(f"Last task status: {status_emoji} {latest_task['status']}")

        # Guidance based on context
        guidance = self._generate_contextual_guidance()
        if guidance:
            context_parts.append(f"\n=== CONTEXTUAL GUIDANCE ===")
            context_parts.extend(guidance)

        return "\n".join(context_parts)

    def _track_execution_results(self, instruction: str, commands: List[Dict], executed_count: int) -> None:
        """
        Track execution results for pattern analysis.

        Args:
            instruction: The instruction that was executed
            commands: List of commands that were attempted
            executed_count: Number of commands that executed successfully
        """
        # Track retry attempts
        if instruction in self.retry_attempts:
            self.retry_attempts[instruction] += 1
        else:
            self.retry_attempts[instruction] = 1

        # Track failures
        if executed_count < len(commands):
            failed_count = len(commands) - executed_count
            failure_entry = {
                "instruction": instruction,
                "commands": commands,
                "executed_count": executed_count,
                "failed_count": failed_count,
                "timestamp": asyncio.get_event_loop().time(),
                "error_type": "partial_execution",
                "command": f"{failed_count} of {len(commands)} commands failed",
                "reason": "Some commands in the sequence failed to execute"
            }
            self.execution_failures.append(failure_entry)

            # Limit failure history
            if len(self.execution_failures) > 50:
                self.execution_failures = self.execution_failures[-25:]

        logger.debug(f"Tracked execution: {instruction} - {executed_count}/{len(commands)} succeeded")

    def _get_failed_commands_from_entry(self, history_entry: Dict) -> str:
        """Extract failed command information from a history entry."""
        commands = history_entry.get("commands", [])
        executed_count = history_entry.get("executed_count", 0)

        if executed_count < len(commands):
            failed_commands = commands[executed_count:]
            return ", ".join([cmd.get("action", "unknown") for cmd in failed_commands])
        return ""

    def _get_recent_failures(self, limit: int = 5) -> List[Dict]:
        """Get recent execution failures."""
        return self.execution_failures[-limit:] if self.execution_failures else []

    def _analyze_success_patterns(self) -> List[str]:
        """Analyze command history to identify success patterns."""
        patterns = []

        if len(self.command_history) < 2:
            return patterns

        # Analyze command types that work well
        successful_commands = {}
        failed_commands = {}

        for entry in self.command_history[-10:]:  # Last 10 entries
            success_rate = entry.get("success_rate", 0)
            commands = entry.get("commands", [])

            for cmd in commands:
                action = cmd.get("action", "unknown")
                if success_rate == 1.0:
                    successful_commands[action] = successful_commands.get(action, 0) + 1
                elif success_rate < 0.5:
                    failed_commands[action] = failed_commands.get(action, 0) + 1

        # Identify reliable command types
        reliable_commands = [
            action for action, count in successful_commands.items()
            if count >= 2 and failed_commands.get(action, 0) == 0
        ]

        if reliable_commands:
            patterns.append(f"✅ Reliable commands: {', '.join(reliable_commands)}")

        # Identify problematic command types
        problematic_commands = [
            action for action, count in failed_commands.items()
            if count >= 2 and successful_commands.get(action, 0) < count
        ]

        if problematic_commands:
            patterns.append(f"⚠️ Problematic commands: {', '.join(problematic_commands)}")

        # Check for timing patterns
        recent_entries = self.command_history[-5:]
        if len(recent_entries) >= 3:
            avg_success_rate = sum(entry.get("success_rate", 0) for entry in recent_entries) / len(recent_entries)
            if avg_success_rate < 0.7:
                patterns.append("⚠️ Recent success rate is low - consider simpler commands or asking for user input")

        return patterns

    def _generate_contextual_guidance(self) -> List[str]:
        """Generate contextual guidance based on current state and history."""
        guidance = []

        # Check for repeated failures
        if len(self.execution_failures) >= 3:
            recent_failures = self.execution_failures[-3:]
            if all(f["error_type"] == "partial_execution" for f in recent_failures):
                guidance.append("🔄 Multiple recent failures detected - consider breaking down complex tasks into simpler steps")

        # Check for retry patterns
        high_retry_instructions = [
            instruction for instruction, count in self.retry_attempts.items()
            if count >= 3
        ]
        if high_retry_instructions:
            guidance.append("🔄 Some instructions have been retried multiple times - consider asking user for clarification")

        # Check recent success rate
        if len(self.command_history) >= 3:
            recent_success_rates = [entry.get("success_rate", 0) for entry in self.command_history[-3:]]
            avg_success_rate = sum(recent_success_rates) / len(recent_success_rates)

            if avg_success_rate < 0.5:
                guidance.append("⚠️ Low recent success rate - consider using request_input to get user guidance")
            elif avg_success_rate == 1.0:
                guidance.append("✅ Recent commands are executing successfully - continue with current approach")

        # Check for user interaction opportunities
        if not self.user_interactions and len(self.command_history) >= 5:
            guidance.append("💬 Consider using request_input to get user preferences or clarification")

        # Check for task completion opportunities
        if not self.completed_tasks and len(self.command_history) >= 3:
            guidance.append("✅ Consider using task_complete to provide status updates to the user")

        # Check for pending user input
        if self.computer_controller.has_pending_user_input():
            guidance.append("⏳ There is a pending user input request - wait for user response before proceeding")

        return guidance

    def get_context_summary(self) -> Dict:
        """Get a summary of the current context for debugging/monitoring."""
        return {
            "total_commands_executed": len(self.command_history),
            "recent_success_rate": (
                sum(entry.get("success_rate", 0) for entry in self.command_history[-5:]) /
                min(5, len(self.command_history))
            ) if self.command_history else 0,
            "total_failures": len(self.execution_failures),
            "retry_attempts": dict(self.retry_attempts),
            "user_interactions": len(self.user_interactions),
            "completed_tasks": len(self.completed_tasks),
            "context_window_size": self.context_window_size
        }

    def set_context_window_size(self, size: int) -> None:
        """Set the number of recent commands to include in context."""
        self.context_window_size = max(1, min(size, 20))  # Limit between 1 and 20
        logger.info(f"Context window size set to {self.context_window_size}")

    def clear_execution_failures(self) -> None:
        """Clear the execution failure history."""
        self.execution_failures.clear()
        logger.info("Execution failure history cleared")

    def clear_retry_attempts(self) -> None:
        """Clear the retry attempt tracking."""
        self.retry_attempts.clear()
        logger.info("Retry attempt tracking cleared")
