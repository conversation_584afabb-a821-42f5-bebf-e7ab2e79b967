"""Main application entry point."""

import asyncio
import signal
import sys
from pathlib import Path

import typer
from loguru import logger
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt

from .command_interpreter import CommandInterpreter
from .config import settings, validate_environment

app = typer.Typer(help="Gemini Computer Control - AI-powered computer automation")
console = Console()


def setup_logging():
    """Setup logging configuration."""
    logger.remove()  # Remove default handler

    # Add console handler
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )

    # Add file handler
    logger.add(
        settings.log_file,
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )


def check_environment():
    """Check environment configuration and display warnings."""
    errors = validate_environment()

    if errors:
        console.print("\n[bold red]⚠️  Configuration Issues:[/bold red]")
        for error in errors:
            console.print(f"  • {error}")

        if not typer.confirm("\nDo you want to continue anyway?"):
            raise typer.Exit(1)


@app.command()
def single(
    instruction: str = typer.Argument(..., help="Instruction for the AI to execute"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Execute a single instruction."""
    if verbose:
        logger.remove()
        setup_logging()

    check_environment()

    async def run_single():
        interpreter = CommandInterpreter()
        try:
            result = await interpreter.execute_single_instruction(instruction)

            if result["success"]:
                console.print(f"\n[green]✅ Success![/green] {result['message']}")
                console.print(f"Commands executed: {result['commands_executed']}")
            else:
                console.print(f"\n[red]❌ Failed![/red] {result.get('error', 'Unknown error')}")

        except KeyboardInterrupt:
            console.print("\n[yellow]Operation cancelled by user[/yellow]")
        except Exception as e:
            console.print(f"\n[red]Error: {e}[/red]")
        finally:
            await interpreter.stop()

    asyncio.run(run_single())


@app.command()
def autonomous(
    instruction: str = typer.Option("", "--instruction", "-i", help="Initial instruction"),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Enable verbose logging")
):
    """Start autonomous mode with continuous screen monitoring."""
    if verbose:
        logger.remove()
        setup_logging()

    check_environment()

    # Display warning
    console.print(Panel(
        "[bold red]⚠️  AUTONOMOUS MODE WARNING ⚠️[/bold red]\n\n"
        "This mode will continuously monitor your screen and take actions based on AI analysis.\n"
        "The AI will have control over your mouse and keyboard.\n\n"
        "[bold]Safety features enabled:[/bold]\n"
        f"• Safe mode: {settings.safe_mode}\n"
        f"• Confirmation required: {settings.confirmation_required}\n"
        f"• Emergency stop: {settings.emergency_stop_key}\n\n"
        "[bold yellow]Press Ctrl+C to stop at any time[/bold yellow]",
        title="Safety Notice",
        border_style="red"
    ))

    if not typer.confirm("Do you want to continue?"):
        raise typer.Exit(0)

    if not instruction:
        instruction = Prompt.ask("Enter initial instruction (or leave empty for general assistance)")

    async def run_autonomous():
        interpreter = CommandInterpreter()

        # Setup signal handlers for graceful shutdown
        def signal_handler():
            logger.info("Received shutdown signal")
            asyncio.create_task(interpreter.stop())

        if sys.platform != "win32":
            loop = asyncio.get_event_loop()
            loop.add_signal_handler(signal.SIGINT, signal_handler)
            loop.add_signal_handler(signal.SIGTERM, signal_handler)

        try:
            console.print(f"\n[green]🚀 Starting autonomous mode...[/green]")
            if instruction:
                console.print(f"Initial instruction: {instruction}")

            await interpreter.start_autonomous_mode(instruction)

        except KeyboardInterrupt:
            console.print("\n[yellow]Autonomous mode stopped by user[/yellow]")
        except Exception as e:
            console.print(f"\n[red]Error in autonomous mode: {e}[/red]")
        finally:
            await interpreter.stop()

    asyncio.run(run_autonomous())


@app.command()
def status():
    """Show current configuration and system status."""
    console.print(Panel(
        f"[bold]Gemini Computer Control Status[/bold]\n\n"
        f"[bold]Live API Configuration:[/bold]\n"
        f"• Model: {settings.gemini_model}\n"
        f"• Response modality: {settings.live_api_response_modality}\n"
        f"• Session timeout: {settings.live_api_session_timeout}s\n"
        f"• Reconnect attempts: {settings.live_api_reconnect_attempts}\n\n"
        f"[bold]Video Streaming:[/bold]\n"
        f"• Frame rate: {settings.video_frame_rate} FPS\n"
        f"• Max dimension: {settings.video_max_dimension}px\n"
        f"• Video quality: {settings.video_quality}%\n"
        f"• Monitor: {settings.screen_monitor}\n\n"
        f"[bold]Legacy Settings:[/bold]\n"
        f"• Screen capture interval: {settings.screen_capture_interval}s\n"
        f"• Screen capture quality: {settings.screen_capture_quality}%\n\n"
        f"[bold]Security:[/bold]\n"
        f"• Safe mode: {settings.safe_mode}\n"
        f"• Confirmation required: {settings.confirmation_required}\n"
        f"• Emergency stop key: {settings.emergency_stop_key}\n"
        f"• Allowed applications: {', '.join(settings.allowed_applications)}\n\n"
        f"[bold]Supported Commands:[/bold]\n"
        f"• Basic: click, type, key_press, scroll, move_mouse, wait\n"
        f"• Interactive: request_input, task_complete, hotkey\n",
        title="System Status (Live API)",
        border_style="blue"
    ))

    # Check environment
    errors = validate_environment()
    if errors:
        console.print("\n[bold red]Configuration Issues:[/bold red]")
        for error in errors:
            console.print(f"  • {error}")
    else:
        console.print("\n[green]✅ Configuration looks good![/green]")


@app.command()
def test_capture():
    """Test screen capture functionality."""
    from .screen_recorder import ScreenRecorder

    console.print("Testing screen capture...")

    with ScreenRecorder() as recorder:
        image_data = recorder.capture_screen()

        if image_data:
            console.print(f"[green]✅ Screen capture successful![/green]")
            console.print(f"Image size: {len(image_data)} bytes")
            console.print(f"Screen info: {recorder.get_screen_info()}")
        else:
            console.print("[red]❌ Screen capture failed![/red]")


@app.command()
def test_commands():
    """Test the new command types (user input, task completion, hotkey)."""
    check_environment()

    async def run_command_tests():
        from .computer_controller import ComputerController

        controller = ComputerController()
        console.print("[blue]Testing new command types...[/blue]\n")

        # Test 1: User Input Command
        console.print("[yellow]Test 1: User Input Command[/yellow]")
        input_cmd = {
            "action": "request_input",
            "prompt": "What is your favorite color?",
            "input_type": "choice",
            "choices": ["Red", "Blue", "Green", "Yellow"]
        }

        try:
            success = await controller.execute_command(input_cmd)
            if success:
                response = controller.get_user_input_response()
                console.print(f"[green]✅ User input test successful![/green]")
                console.print(f"Response: {response}")
            else:
                console.print("[red]❌ User input test failed![/red]")
        except Exception as e:
            console.print(f"[red]❌ Error: {e}[/red]")

        console.print()

        # Test 2: Task Completion Command
        console.print("[yellow]Test 2: Task Completion Command[/yellow]")
        task_cmd = {
            "action": "task_complete",
            "status": "success",
            "message": "Successfully tested the new command system",
            "results": {"commands_tested": 3, "success_rate": "100%"}
        }

        try:
            success = await controller.execute_command(task_cmd)
            if success:
                console.print(f"[green]✅ Task completion test successful![/green]")
                latest_task = controller.get_latest_task_result()
                console.print(f"Latest task: {latest_task}")
            else:
                console.print("[red]❌ Task completion test failed![/red]")
        except Exception as e:
            console.print(f"[red]❌ Error: {e}[/red]")

        console.print()

        # Test 3: Safe Hotkey Command
        console.print("[yellow]Test 3: Safe Hotkey Command[/yellow]")
        hotkey_cmd = {
            "action": "hotkey",
            "keys": "ctrl+a"  # Safe hotkey (select all)
        }

        try:
            success = await controller.execute_command(hotkey_cmd)
            if success:
                console.print(f"[green]✅ Hotkey test successful![/green]")
                console.print("Executed Ctrl+A (select all)")
            else:
                console.print("[red]❌ Hotkey test failed![/red]")
        except Exception as e:
            console.print(f"[red]❌ Error: {e}[/red]")

        console.print()
        console.print("[green]🎉 All command tests completed![/green]")

    asyncio.run(run_command_tests())


@app.command()
def test_context():
    """Test the enhanced context system with simulated command history."""
    check_environment()

    async def run_context_test():
        from .command_interpreter import CommandInterpreter

        interpreter = CommandInterpreter()
        console.print("[blue]Testing enhanced context system...[/blue]\n")

        try:
            # Simulate some command history with mixed success/failure
            console.print("[yellow]Simulating command execution history...[/yellow]")

            # Simulate successful commands
            interpreter._track_execution_results("click on button", [
                {"action": "click", "x": 100, "y": 200}
            ], 1)

            # Simulate partial failure
            interpreter._track_execution_results("type text and press enter", [
                {"action": "type", "text": "hello"},
                {"action": "key_press", "key": "enter"},
                {"action": "click", "x": 300, "y": 400}
            ], 2)  # Only 2 out of 3 succeeded

            # Simulate retry attempts
            interpreter._track_execution_results("open file dialog", [
                {"action": "hotkey", "keys": "ctrl+o"}
            ], 0)  # Failed

            interpreter._track_execution_results("open file dialog", [
                {"action": "hotkey", "keys": "ctrl+o"}
            ], 0)  # Failed again

            # Add some user interactions
            interpreter.user_interactions.append({
                "prompt": "Which file should I open?",
                "input_type": "text",
                "response": "document.txt",
                "timestamp": asyncio.get_event_loop().time()
            })

            # Add task completion
            interpreter.completed_tasks.append({
                "status": "partial",
                "message": "Opened some files but couldn't find document.txt",
                "results": {"files_opened": 2, "target_found": False},
                "timestamp": asyncio.get_event_loop().time()
            })

            console.print("[green]✅ Simulated history created[/green]\n")

            # Generate and display context
            console.print("[yellow]Generating AI context...[/yellow]")
            context = interpreter._generate_ai_context("Find and open document.txt")

            console.print("[cyan]Generated Context:[/cyan]")
            console.print("=" * 60)
            console.print(context)
            console.print("=" * 60)

            # Show context summary
            console.print(f"\n[yellow]Context Summary:[/yellow]")
            summary = interpreter.get_context_summary()
            for key, value in summary.items():
                console.print(f"  • {key}: {value}")

            # Test context window size adjustment
            console.print(f"\n[yellow]Testing context window size adjustment...[/yellow]")
            interpreter.set_context_window_size(3)

            shorter_context = interpreter._generate_ai_context("Find and open document.txt")
            console.print(f"Context with window size 3: {len(shorter_context)} characters")

            interpreter.set_context_window_size(10)
            longer_context = interpreter._generate_ai_context("Find and open document.txt")
            console.print(f"Context with window size 10: {len(longer_context)} characters")

            console.print(f"\n[green]🎉 Context system test completed![/green]")

        except Exception as e:
            console.print(f"[red]❌ Error during context test: {e}[/red]")
        finally:
            await interpreter.stop()

    asyncio.run(run_context_test())


if __name__ == "__main__":
    setup_logging()
    app()
