"""Gemini Live API client for processing live video streams."""

import asyncio
import json
from typing import Dict, List, Optional, AsyncGenerator
import time

from google import genai
from google.genai import types
from loguru import logger

from .config import settings


class GeminiClient:
    """Client for interacting with the Gemini Live API."""

    def __init__(self):
        # Configure the API client
        self.client = genai.Client(api_key=settings.gemini_api_key)
        self.model = settings.gemini_model

        # Session management
        self.session = None
        self.session_context = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.last_response_time = time.time()

        # Response handling
        self.response_queue = asyncio.Queue()
        self.current_response_text = ""

        # System instruction for Live API
        self.system_instruction = types.Content(
            parts=[
                types.Part(
                    text="""You are an AI assistant that can control a computer by analyzing live video feed and providing specific commands.

Your task is to:
1. Analyze the provided live video stream of the computer screen
2. Understand what the user wants to accomplish
3. Provide specific computer control commands in JSON format

Available commands:
- click: {"action": "click", "x": 100, "y": 200}
- type: {"action": "type", "text": "hello world"}
- key_press: {"action": "key_press", "key": "enter"}
- scroll: {"action": "scroll", "direction": "up|down", "amount": 3}
- move_mouse: {"action": "move_mouse", "x": 100, "y": 200}
- wait: {"action": "wait", "seconds": 2}
- request_input: {"action": "request_input", "prompt": "What is your name?", "input_type": "text|number|choice", "choices": ["option1", "option2"]}
- task_complete: {"action": "task_complete", "status": "success|partial|failed", "message": "Task description", "results": "any relevant data"}
- hotkey: {"action": "hotkey", "keys": "ctrl+c"}

IMPORTANT RULES:
1. Always respond with valid JSON containing a "commands" array
2. Be precise with coordinates - analyze the video feed carefully
3. If you're unsure about an action, include a "wait" command first
4. For safety, avoid actions that could be destructive
5. If the task is unclear, respond with: {"commands": [], "message": "Please clarify what you want me to do"}
6. You are receiving a live video feed, so respond to the current state of the screen
7. Be responsive to changes in the video feed

Example response:
{
    "reasoning": "I can see a search field on the screen and the user wants to search for 'Hello World'",
    "commands": [
        {"action": "click", "x": 150, "y": 300},
        {"action": "type", "text": "Hello World"},
        {"action": "key_press", "key": "enter"}
    ],
    "message": "Clicked on search field and typed 'Hello World'"
}"""
                )
            ]
        )

    async def create_live_session(self):
        """
        Create a Live API session context manager.

        Returns:
            Async context manager for the Live API session
        """
        # Configure the Live API session
        config = types.LiveConnectConfig(
            response_modalities=[settings.live_api_response_modality],
            system_instruction=self.system_instruction
        )

        # Return the context manager (don't enter it here)
        return self.client.aio.live.connect(model=self.model, config=config)

    async def setup_session(self, session, user_instruction: str = "", context: str = "") -> None:
        """
        Setup the session after connection.

        Args:
            session: The connected Live API session
            user_instruction: Initial user instruction
            context: Context from previous actions
        """
        self.session = session
        self.is_connected = True
        self.reconnect_attempts = 0
        logger.info("Connected to Gemini Live API")

        # Send initial context if provided
        if user_instruction or context:
            await self.send_initial_context(user_instruction, context)

    async def send_initial_context(self, user_instruction: str, context: str) -> None:
        """Send initial context to the Live API session."""
        if not self.session or not self.is_connected:
            return

        try:
            context_message = ""
            if context:
                context_message += f"CONTEXT FROM PREVIOUS ACTIONS:\n{context}\n\n"

            if user_instruction:
                context_message += f"User instruction: {user_instruction}\n\n"
            else:
                context_message += "Analyze the live video feed and suggest appropriate actions.\n\n"

            context_message += "IMPORTANT: You are receiving a live video feed of the computer screen. Respond with appropriate commands based on what you see."

            await self.session.send_client_content(
                turns={"role": "user", "parts": [{"text": context_message}]},
                turn_complete=True
            )

            logger.debug("Sent initial context to Live API")

        except Exception as e:
            logger.error(f"Failed to send initial context: {e}")

    async def send_video_frame(self, frame_data: str) -> None:
        """
        Send a video frame to the Live API.

        Args:
            frame_data: Base64 encoded video frame
        """
        if not self.session or not self.is_connected:
            return

        try:
            await self.session.send_realtime_input(
                video={
                    "data": frame_data,
                    "mimeType": "image/jpeg"
                }
            )

            self.last_response_time = time.time()

        except Exception as e:
            logger.error(f"Failed to send video frame: {e}")
            await self._handle_connection_error()

    async def _handle_responses(self) -> None:
        """Handle incoming responses from the Live API."""
        if not self.session:
            return

        try:
            async for response in self.session.receive():
                if response.text is not None:
                    self.current_response_text += response.text

                # Check if we have a complete response
                if response.server_content and response.server_content.turn_complete:
                    await self._process_complete_response()

        except Exception as e:
            logger.error(f"Error handling responses: {e}")
            await self._handle_connection_error()

    async def _process_complete_response(self) -> None:
        """Process a complete response and add it to the queue."""
        if not self.current_response_text.strip():
            return

        try:
            # Try to parse as JSON
            result = json.loads(self.current_response_text)
            if "commands" not in result:
                result["commands"] = []

        except json.JSONDecodeError:
            # If not valid JSON, try to extract JSON from the response
            import re
            json_match = re.search(r'\{.*\}', self.current_response_text, re.DOTALL)
            if json_match:
                try:
                    result = json.loads(json_match.group())
                    if "commands" not in result:
                        result["commands"] = []
                except json.JSONDecodeError:
                    result = {
                        "commands": [],
                        "message": f"Could not parse response: {self.current_response_text}"
                    }
            else:
                result = {
                    "commands": [],
                    "message": f"Could not parse response: {self.current_response_text}"
                }

        # Add to response queue
        await self.response_queue.put(result)

        # Clear current response
        self.current_response_text = ""

        logger.debug(f"Processed complete response: {result}")

    async def get_next_response(self, timeout: float = 10.0) -> Optional[Dict]:
        """
        Get the next response from the Live API.

        Args:
            timeout: Timeout in seconds

        Returns:
            Response dictionary or None if timeout
        """
        try:
            return await asyncio.wait_for(self.response_queue.get(), timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for response")
            return None

    async def _handle_connection_error(self) -> None:
        """Handle connection errors and attempt reconnection."""
        self.is_connected = False

        if self.reconnect_attempts < settings.live_api_reconnect_attempts:
            self.reconnect_attempts += 1
            logger.info(f"Attempting reconnection {self.reconnect_attempts}/{settings.live_api_reconnect_attempts}")

            await asyncio.sleep(settings.live_api_reconnect_delay)
            # Note: Reconnection logic would be handled by the command interpreter
        else:
            logger.error("Max reconnection attempts reached")

    async def cleanup_session(self) -> None:
        """Clean up session state."""
        self.session = None
        self.session_context = None
        self.is_connected = False
        logger.info("Session cleaned up")

    def is_session_healthy(self) -> bool:
        """Check if the session is healthy."""
        if not self.is_connected or not self.session:
            return False

        # Check if we've received responses recently
        time_since_last_response = time.time() - self.last_response_time
        return time_since_last_response < settings.live_api_session_timeout

    async def validate_commands(self, commands: List[Dict]) -> List[Dict]:
        """
        Validate and filter commands for safety.

        Args:
            commands: List of command dictionaries

        Returns:
            Filtered list of safe commands
        """
        safe_commands = []

        for cmd in commands:
            if not isinstance(cmd, dict) or "action" not in cmd:
                logger.warning(f"Invalid command format: {cmd}")
                continue

            action = cmd["action"]

            # Validate command structure
            if action == "click" and ("x" not in cmd or "y" not in cmd):
                logger.warning(f"Click command missing coordinates: {cmd}")
                continue
            elif action == "type" and "text" not in cmd:
                logger.warning(f"Type command missing text: {cmd}")
                continue
            elif action == "key_press" and "key" not in cmd:
                logger.warning(f"Key press command missing key: {cmd}")
                continue
            elif action == "scroll" and ("direction" not in cmd or "amount" not in cmd):
                logger.warning(f"Scroll command missing parameters: {cmd}")
                continue
            elif action == "move_mouse" and ("x" not in cmd or "y" not in cmd):
                logger.warning(f"Move mouse command missing coordinates: {cmd}")
                continue
            elif action == "wait" and "seconds" not in cmd:
                logger.warning(f"Wait command missing seconds: {cmd}")
                continue
            elif action == "request_input" and "prompt" not in cmd:
                logger.warning(f"Request input command missing prompt: {cmd}")
                continue
            elif action == "task_complete" and "status" not in cmd:
                logger.warning(f"Task complete command missing status: {cmd}")
                continue
            elif action == "hotkey" and "keys" not in cmd:
                logger.warning(f"Hotkey command missing keys: {cmd}")
                continue

            # Additional safety checks
            if action == "type":
                # Limit text length
                if len(cmd["text"]) > 1000:
                    logger.warning(f"Text too long, truncating: {len(cmd['text'])} chars")
                    cmd["text"] = cmd["text"][:1000]
            elif action == "request_input":
                # Validate input type
                input_type = cmd.get("input_type", "text")
                if input_type not in ["text", "number", "choice"]:
                    logger.warning(f"Invalid input type, defaulting to text: {input_type}")
                    cmd["input_type"] = "text"

                # Limit prompt length
                if len(cmd["prompt"]) > 500:
                    logger.warning(f"Prompt too long, truncating: {len(cmd['prompt'])} chars")
                    cmd["prompt"] = cmd["prompt"][:500]

                # Validate choices for choice input type
                if input_type == "choice" and "choices" in cmd:
                    choices = cmd["choices"]
                    if not isinstance(choices, list) or len(choices) > 10:
                        logger.warning(f"Invalid or too many choices, limiting to 10")
                        cmd["choices"] = choices[:10] if isinstance(choices, list) else []
            elif action == "task_complete":
                # Validate status
                status = cmd.get("status", "success")
                if status not in ["success", "partial", "failed"]:
                    logger.warning(f"Invalid status, defaulting to success: {status}")
                    cmd["status"] = "success"

                # Limit message length
                message = cmd.get("message", "")
                if len(message) > 500:
                    logger.warning(f"Message too long, truncating: {len(message)} chars")
                    cmd["message"] = message[:500]
            elif action == "hotkey":
                # Validate hotkey format
                keys = cmd.get("keys", "")
                if not keys or not isinstance(keys, str):
                    logger.warning(f"Invalid hotkey format: {keys}")
                    continue

                # Basic format validation (will be further validated in controller)
                if len(keys) > 50:  # Reasonable limit
                    logger.warning(f"Hotkey too long: {keys}")
                    continue

            safe_commands.append(cmd)

        return safe_commands
