"""Configuration management for Gemini Computer Control."""

import os
from pathlib import Path
from typing import List, Optional

from dotenv import load_dotenv
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Gemini API Configuration
    gemini_api_key: str = Field(..., env="GEMINI_API_KEY")
    gemini_model: str = Field("gemini-2.0-flash-live-001", env="GEMINI_MODEL")

    # Screen Recording Configuration
    screen_capture_interval: float = Field(2.0, env="SCREEN_CAPTURE_INTERVAL")
    screen_capture_quality: int = Field(80, env="SCREEN_CAPTURE_QUALITY")
    screen_monitor: int = Field(0, env="SCREEN_MONITOR")

    # Live API Video Streaming Configuration
    video_frame_rate: float = Field(1.0, env="VIDEO_FRAME_RATE")  # FPS for live video feed
    video_max_dimension: int = Field(1280, env="VIDEO_MAX_DIMENSION")  # Max width/height
    video_quality: int = Field(70, env="VIDEO_QUALITY")  # JPEG quality for video frames

    # Live API Session Configuration
    live_api_response_modality: str = Field("TEXT", env="LIVE_API_RESPONSE_MODALITY")  # TEXT or AUDIO
    live_api_session_timeout: int = Field(300, env="LIVE_API_SESSION_TIMEOUT")  # Session timeout in seconds
    live_api_reconnect_attempts: int = Field(3, env="LIVE_API_RECONNECT_ATTEMPTS")
    live_api_reconnect_delay: float = Field(5.0, env="LIVE_API_RECONNECT_DELAY")

    # Computer Control Configuration
    mouse_speed: float = Field(1.0, env="MOUSE_SPEED")
    typing_interval: float = Field(0.05, env="TYPING_INTERVAL")
    confirmation_required: bool = Field(True, env="CONFIRMATION_REQUIRED")
    safe_mode: bool = Field(True, env="SAFE_MODE")

    # API Rate Limiting
    max_requests_per_minute: int = Field(30, env="MAX_REQUESTS_PER_MINUTE")
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT")

    # Logging Configuration
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: str = Field("gemini_control.log", env="LOG_FILE")

    # Security Configuration
    allowed_applications: List[str] = Field(
        ["TextEdit", "Calculator", "Notes"],
        env="ALLOWED_APPLICATIONS"
    )
    dangerous_commands_require_confirmation: bool = Field(
        True,
        env="DANGEROUS_COMMANDS_REQUIRE_CONFIRMATION"
    )
    emergency_stop_key: str = Field("ctrl+shift+q", env="EMERGENCY_STOP_KEY")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def load_settings() -> Settings:
    """Load application settings from environment variables."""
    # Load .env file if it exists
    env_path = Path(".env")
    if env_path.exists():
        load_dotenv(env_path)

    return Settings()


# Global settings instance
settings = load_settings()


# Validation functions
def validate_api_key() -> bool:
    """Validate that the Gemini API key is set."""
    return bool(settings.gemini_api_key and settings.gemini_api_key != "your_gemini_api_key_here")


def validate_environment() -> List[str]:
    """Validate the environment configuration and return any errors."""
    errors = []

    if not validate_api_key():
        errors.append("GEMINI_API_KEY is not set or is using the default placeholder value")

    if settings.screen_capture_interval < 0.5:
        errors.append("SCREEN_CAPTURE_INTERVAL should be at least 0.5 seconds to avoid API rate limits")

    if settings.max_requests_per_minute > 60:
        errors.append("MAX_REQUESTS_PER_MINUTE should not exceed 60 to respect API limits")

    # Live API specific validations
    if settings.video_frame_rate < 0.1 or settings.video_frame_rate > 10:
        errors.append("VIDEO_FRAME_RATE should be between 0.1 and 10 FPS for optimal performance")

    if settings.video_max_dimension < 480 or settings.video_max_dimension > 1920:
        errors.append("VIDEO_MAX_DIMENSION should be between 480 and 1920 pixels")

    if settings.live_api_response_modality not in ["TEXT", "AUDIO"]:
        errors.append("LIVE_API_RESPONSE_MODALITY must be either 'TEXT' or 'AUDIO'")

    if settings.live_api_session_timeout < 60:
        errors.append("LIVE_API_SESSION_TIMEOUT should be at least 60 seconds")

    return errors
